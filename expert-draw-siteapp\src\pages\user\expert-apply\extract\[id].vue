<!-- 项目申请记录查询页面 -->
<template>
  <div class="records-page">
    <FuniList
      ref="recordsListRef"
      :tabs="tabsConfig"
      :search-placeholder="searchPlaceholder"
      item-key="id"
      @item-click="handleItemClick"
      @tab-change="handleTabChange"
    >
      <template #INHeader>
        <van-notice-bar
          wrapable
          left-icon="info-o"
          :scrollable="false"
          text="完成抽取专家后，后台自动发送短信，需等待专家回复（15分钟超时）"
        />
      </template>
      <template #item="{ item, index }" @click="viewDetail(record)">
         <expertItem :item="item.expert"></expertItem>
        <!-- 操作按钮 -->
        <div class="record-actions" v-if="item.selectStatus=='IN'">
          <van-button
            size="small"
            type="warning"
            plain
            block
            @click.stop="handleExpertEvade(item)"
          >
            专家规避
          </van-button>
        </div>
      </template>
    </FuniList>

    <!-- 专家规避弹框 -->
    <van-popup v-model:show="showEvadeDialog" position="bottom" round>
      <div class="evade-dialog">
        <div class="dialog-header">
          <h3>对 {{ currentExpert?.expert?.name }} 申请规避</h3>
        </div>

        <div class="dialog-content">
          <van-radio-group v-model="selectContent">
            <van-radio name="与受评项目单位存在需要回避"
              >与受评项目单位存在需要回避</van-radio
            >
            <van-radio name="相同单位且专业类别相同需要回避"
              >相同单位且专业类别相同需要回避</van-radio
            >
          </van-radio-group>

          <div class="reason-input">
            <van-field
              v-model="evadeReason"
              type="textarea"
              placeholder="请输入规避理由"
              rows="3"
              maxlength="200"
              show-word-limit
              autosize
            />
          </div>
        </div>

        <div class="dialog-actions">
          <van-button
            size="large"
            @click="showEvadeDialog = false"
            style="margin-right: 12px; flex: 1"
          >
            返回
          </van-button>
          <van-button
            type="success"
            size="large"
            :loading="evadeLoading"
            @click="confirmEvade"
            style="flex: 2"
          >
            确定
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, reactive } from "vue";
import { useRouter, useRoute } from "vue-router";
import { showToast, showSuccessToast } from "vant";
import { expertApi } from "@/api/expert";
import expertItem from "@/pages/pub/expert/component/list-item.vue";

const router = useRouter();
const route = useRoute();
const recordsListRef = ref();

// 专家规避相关状态
const showEvadeDialog = ref(false);
const currentExpert = ref(null);
const evadeReason = ref("");
const evadeLoading = ref(false);

const selectContent = ref("与受评项目单位存在需要回避");

const extras = reactive({
  IN: "",
  OUT: "",
  UN_CONFIRM: "",
  EVADE: "",
});

// Tabs 配置
const tabsConfig = computed(() => [
  {
    key: "IN",
    title: "入选",
    loadFunction: loadData,
    badge: extras.IN,
    showSearch: false,
    extraParams: {
      selectStatus: "IN",
    },
  },
  {
    key: "OUT",
    title: "淘汰",
    loadFunction: loadData,
    badge: extras.OUT,
    showSearch: false,
    extraParams: {
      selectStatus: "OUT",
    },
  },
  {
    key: "UN_CONFIRM",
    title: "待确认",
    loadFunction: loadData,
    badge: extras.UN_CONFIRM,
    showSearch: false,
    extraParams: {
      selectStatus: "UN_CONFIRM",
    },
  },
  {
    key: "EVADE",
    title: "规避",
    loadFunction: loadData,
    badge: extras.EVADE,
    showSearch: false,
    extraParams: {
      selectStatus: "EVADE",
    },
  },
]);

async function loadData(params) {
  const response = await expertApi.getReviewProject({ id: route.params.id });
  let data = response.data.drawResults.filter(
    (x) => x.selectStatus == params.selectStatus
  );
  for (const key in extras) {
    extras[key] = response.data.drawResults.filter(
      (x) => x.selectStatus == key
    ).length;
  }
  return {
    data: data,
    total: data.length,
  };
}

// 事件处理方法
const handleItemClick = (record, index) => {
  console.log("点击记录:", record, index);
};

const handleTabChange = (tabKey, tab) => {
  console.log("Tab 切换:", tabKey, tab);
};
// 获取性别文本
const getGenderText = (gender) => {
  return window.$enums.getEnumText("Gender", gender);
};

// 处理专家规避
const handleExpertEvade = (expert) => {
  currentExpert.value = expert;
  evadeReason.value = "";
  showEvadeDialog.value = true;
};

// 确认专家规避
const confirmEvade = async () => {
  if (!evadeReason.value.trim()) {
    showToast("请输入规避理由");
    return;
  }

  try {
    evadeLoading.value = true;

    // 调用专家规避接口
    await expertApi.systemEvade(route.params.id, {
      selectContent: selectContent.value+"："+evadeReason.value.trim(),
      resultIds: [currentExpert.value.id],
    });

    showSuccessToast("专家规避申请成功");
    showEvadeDialog.value = false;

    // 刷新列表数据
    if (recordsListRef.value) {
      recordsListRef.value.refresh();
    }
  } catch (error) {
    console.error("专家规避失败:", error);
    showToast(error.message || "专家规避失败，请重试");
  } finally {
    evadeLoading.value = false;
  }
};
</script>

<style lang="scss" scoped>
.records-page {
  height: calc(100vh - 46px);
  background: #f5f5f5;
}
.expert-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  .expert-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f7f8fa;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;

    .van-icon {
      font-size: 24px;
      color: #969799;
    }
  }

  .expert-basic {
    flex: 1;

    .expert-name {
      font-size: 16px;
      font-weight: 600;
      color: #323233;
      margin-bottom: 4px;
    }

    .expert-info {
      font-size: 12px;
      color: #646566;

      span {
        margin-right: 12px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}

.expert-details {
  .detail-row {
    display: flex;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .detail-item {
      flex: 1;
      display: flex;
      flex-direction: column;

      .label {
        font-size: 12px;
        color: #606266;
        margin-bottom: 4px;
      }

      .value {
        font-size: 14px;
        color: #000;
        font-weight: 500;
        &:empty::after {
          content: "--";
        }
      }
    }
  }
}

.record-actions {
  padding-top: 12px;
}

// 专家规避弹框样式
.evade-dialog {
  padding: 24px;

  .dialog-header {
    text-align: center;
    margin-bottom: 24px;

    h3 {
      font-size: 18px;
      font-weight: 600;
      color: #323233;
      margin: 0 0 8px 0;
    }

    .dialog-subtitle {
      font-size: 14px;
      color: #646566;
      margin: 0;
    }
  }

  .dialog-content {
    margin-bottom: 24px;

    .van-radio {
      margin-bottom: 8px;
    }

    .reason-input {
      :deep(.van-field) {
        padding: 12px 16px;
        background-color: #f7f8fa;
        border-radius: 8px;
        border: 1px solid #ebedf0;

        .van-field__control {
          font-size: 14px;
          line-height: 1.5;
        }

        .van-field__word-limit {
          font-size: 12px;
          color: #969799;
        }
      }
    }
  }

  .dialog-actions {
    display: flex;
    gap: 12px;

    .van-button {
      border-radius: 8px;
      font-size: 16px;
      font-weight: 500;
    }
  }
}
</style>

<route lang="json5">
{
  name: "expertExtract",
  meta: {
    title: "抽取专家",
  },
}
</route>
