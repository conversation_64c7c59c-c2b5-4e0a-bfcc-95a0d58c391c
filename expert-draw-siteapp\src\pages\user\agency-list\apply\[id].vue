<!-- 专家抽取申请详情页面 -->
<template>
  <div class="expert-detail-page">
    <!-- 头部标题区域 -->
    <div class="header-section">
      <div class="header-content">
        <div class="title-line">成都市农业农村局项目专家抽取</div>
      </div>
    </div>
    <van-cell-group>
      <van-cell title="申请部门" :value="detailData.department?.name" />
      <van-cell title="经办人" :value="detailData.name || '-'" />
      <van-cell title="经办人联系方式" :value="detailData.name || '-'" />
    </van-cell-group>
    <div class="section">
      <div class="title">一、项目基本情况</div>
      <van-cell-group>
        <van-cell title="申请部门" :value="detailData.department?.name" />
        <van-cell title="经办人" :value="detailData.name || '-'" />
        <van-cell title="经办人联系方式" :value="detailData.name || '-'" />
      </van-cell-group>
    </div>
    <div class="section">
      <div class="title">二、申请用途</div>
      <van-cell-group>
        <van-cell title="申请部门" :value="detailData.department?.name" />
        <van-cell title="经办人" :value="detailData.name || '-'" />
        <van-cell title="经办人联系方式" :value="detailData.name || '-'" />
      </van-cell-group>
    </div>
    <div class="section">
      <van-cell-group>
        <van-cell title="三、提供附件" label="领导批示、党组会议纪要、党组会请示议题材料、政府采购计划备案表等" />
        <van-cell title="申请部门" :value="detailData.department?.name" />
        <van-cell title="经办人" :value="detailData.name || '-'" />
        <van-cell title="经办人联系方式" :value="detailData.name || '-'" />
      </van-cell-group>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { showToast } from "vant";
import { expertApi } from "@/api/expert";
import moment from "moment";

const route = useRoute();
const router = useRouter();

// 响应式数据
const loading = ref(true);
const detailData = ref({});

// 获取详情数据
const loadDetail = async () => {
  try {
    loading.value = true;
    const id = route.params.id;
    const response = await expertApi.projectDetail(id);
    detailData.value = response.data;
  } finally {
    loading.value = false;
  }
};

// 格式化时间
const formatDateTime = (dateStr) => {
  if (!dateStr) return "-";
  return moment(dateStr).format("YYYY-MM-DD HH:mm:ss");
};

// 获取项目类型文本
const getProjectTypeText = (type) => {
  const typeMap = {
    GOVERNMENT: "机关",
    DIRECTLY: "直属单位",
  };
  return typeMap[type] || "-";
};

// 获取专家来源文本
const getExpertSourceText = (source) => {
  const sourceMap = {
    OUR: "我局专家库",
    PROVINCIAL: "省财政厅专家库",
  };
  return sourceMap[source] || "-";
};

// 预览文件
const previewFile = (file) => {
  showToast("文件预览功能开发中");
  // TODO: 实现文件预览功能
};

// 页面初始化
onMounted(() => {
  loadDetail();
});
</script>

<style lang="scss" scoped>
.expert-detail-page {
  .header-section {
    background: linear-gradient(135deg, #07c160 0%, #05a854 100%);
    padding: 20px 16px;
    color: white;

    .header-content {
      text-align: center;

      .title-line {
        font-size: 18px;
        font-weight: 600;
        line-height: 1.4;
      }
    }
  }
  .section{
    background-color: #fff;
    margin-top: 12px;
    .title{
        padding: 20px 16px;
        font-weight: bold;
    }
  }
}
</style>
