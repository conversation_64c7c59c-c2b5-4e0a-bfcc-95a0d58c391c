<!-- 无预算项目前期工作抽取代理机构 -->
<template>
  <div class="agency-add-page">
    <!-- 表单内容 -->
    <FuniForm
      ref="formRef"
      v-model="formData"
      :form-config="formConfig"
      :submit-loading="submitting"
      submit-text="提交申请"
      @submit="onSubmit"
      @failed="onFormFailed"
    />
    <!-- 按钮 -->
    <div class="filter-actions">
      <van-button class="btn" block @click="onBack" size="normal"
        >返回</van-button
      >
      <van-button
        class="btn"
        block
        type="primary"
        @click="btnSubmit"
        size="normal"
        >提交</van-button
      >
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from "vue";
import { useRouter } from "vue-router";
import { showToast, showSuccessToast } from "vant";
import { expertApi } from "@/api/expert";
import { useUserStore } from "@/stores";
import FuniForm from "@/components/FuniForm.vue";

const router = useRouter();
const userStore = useUserStore();

// 响应式数据
const formRef = ref();
const submitting = ref(false);

// 表单数据
const formData = reactive({
  name: "",
  type: "",
  departmentId: userStore.userDepartment.id,
  departmentText: userStore.userDepartment.name,
  purpose: "",
  planType: "",
  items: "",
  number: "",
  fundsBudget: "",
  projectDate: [],
  districtDep: false,
});

// 表单配置
const formConfig = computed(() => [
  {
    type: "input",
    prop: "name",
    label: "项目名称",
    placeholder: "请输入项目名称",
    required: true,
    rules: [{ required: true, message: "请输入项目名称" }],
  },
  {
    type: "select",
    prop: "type",
    label: "项目类型",
    placeholder: "请选择项目类型",
    required: true,
    options: window.$enums.getEnums("ProjectType"),
    rules: [{ required: true, message: "请选择项目类型" }],
  },
  {
    prop: "departmentText",
    label: "承办处室",
    readonly: true,
    required: true,
    rules: [{ required: true, message: "承办处室信息获取失败" }],
  },
  {
    type: "textarea",
    prop: "purpose",
    label: "采购内容(用途)",
    placeholder: "请输入采购内容",
    rows: 3,
    autosize: true,
  },
  {
    type: "select",
    prop: "planType",
    label: "预算分类",
    placeholder: "请选择预算分类",
    required: true,
    options: window.$enums.getEnums("ProjectPlanType"),
    rules: [{ required: true, message: "请选择预算分类" }],
  },
  {
    type: "input",
    prop: "items",
    label: "采购品目",
    placeholder: "请输入采购品目",
  },
  {
    type: "number",
    prop: "number",
    label: "数量",
    placeholder: "请输入数量",
  },
  {
    type: "number",
    prop: "fundsBudget",
    label: "经费预算",
    placeholder: "请输入经费预算",
    suffix: "万元",
  },
  {
    type: "date",
    prop: "projectDate",
    label: "立项时间",
    placeholder: "请选择立项时间",
  },
]);

// 返回上一页
const onBack = () => {
  router.back();
};

// 提交表单
const btnSubmit = async () => {
  try {
    let values = await formRef.value.validate();
    submitting.value = true;
    // 调用API提交
    await expertApi.createProject({...formData,...values});

    showSuccessToast("提交成功");

    // 延迟返回列表页
    setTimeout(() => {
      router.replace("/user/agency-list");
    }, 1500);
  } finally {
    submitting.value = false;
  }
};

</script>

<style lang="scss" scoped>
.agency-add-page {
  .filter-actions {
    display: flex;
    gap: 16px;
    padding: 16px;
  }
}
</style>
<route lang="json5">
{
  name: "agencyAdd",
  meta: {
    title: "无预算项目前期工作抽取代理机构",
  },
}
</route>
