<!-- 专家规避审批页面 -->
<template>
  <div class="progress-page">
    <!-- 加载状态 -->
    <van-loading v-if="loading" class="loading-container" vertical>
      加载中...
    </van-loading>

    <!-- 进度内容 -->
    <div v-else-if="progressData" class="progress-container">
      <!-- 进度步骤 -->
      <div class="steps-section">
        <div class="custom-steps" v-if="progressData.flows.length > 0">
          <div
            class="step-item"
            v-for="(step, index) in progressData.flows"
            :key="index"
            :class="{ active: step.activity }"
          >
            <div class="step-icon" :class="{ active: step.activity }">
              {{ index + 1 }}
            </div>
            <div class="step-title">{{ step.conf.flowNode }}</div>
            <div
              v-if="index < progressData.flows.length - 1"
              class="step-line"
              :class="{ active: step.activity && index < currentStepIndex }"
            ></div>
          </div>
        </div>
        <div v-else class="no-steps">暂无流程步骤信息</div>
      </div>

      <!-- 动态生成的审批节点 -->
      <template v-for="(flow, index) in progressData.flows">
        <div :key="index" class="section" v-if="flow.enabled">
          <div class="section-title">{{ getFlowNodeTitle(flow) }}</div>

          <!-- 审批人信息 -->
          <van-cell-group>
            <van-cell
              :title="getApproverTitle(flow)"
              :value="getApproverName(flow)"
            />
            <van-cell
              v-if="index == 0"
              title="经办人联系方式"
              :value="getApproverContact(flow)"
            />
            <van-cell
              v-if="index > 0"
              title="任职岗位"
              :value="getApproverPost(flow)"
            />
            <van-cell
              v-if="index > 0"
              title="任职处室"
              :value="getApproverDept(flow)"
            />
            <van-cell
              v-if="index == 0"
              title="需求申请时间"
              :value="formatDateTime(progressData.createTime)"
            />
            <van-cell
              v-if="index > 0 && flow.approvalStatus"
              title="审批意见"
              :value="flow.approvalStatus == 'PASS' ? '通过' : '驳回'"
            />
            <van-cell
              v-if="index > 0 && flow.approvalStatus"
              title="审批时间"
              :value="flow.approvalTime"
            />
            <van-cell
              v-if="index > 0 && flow.approvalStatus"
              title="补充说明"
              :value="flow.approvalContent"
            />
          </van-cell-group>
        </div>
      </template>

      <!-- 审批意见 -->
      <van-form ref="approvalFormRef" required="auto" v-if="activity">
        <van-field
          name="approvalStatus"
          label="审批意见"
          :rules="[{ required: true, message: '请选择审批意见' }]"
        >
          <template #input>
            <van-radio-group
              v-model="approvalForm.approvalStatus"
              direction="horizontal"
            >
              <van-radio name="PASS">通过</van-radio>
              <van-radio name="REJECT">驳回</van-radio>
            </van-radio-group>
          </template>
        </van-field>
        <van-field
          v-model="approvalForm.approvalContent"
          name="approvalContent"
          label="补充说明"
          type="textarea"
          placeholder="请输入补充说明"
          rows="4"
          maxlength="200"
          show-word-limit
          :rules="[{ required: true, message: '请输入补充说明' }]"
          class="comment-field"
        />
      </van-form>

      <!-- 项目详情 -->
      <div class="section">
        <div class="section-header">
          <span class="project-title">专家规避申请详情</span>
        </div>

        <van-cell-group>
          <van-cell title="项目名称" :value="progressData.name" />
          <van-cell title="规避理由" :value="progressData.evadeContent" />
        </van-cell-group>
        
        <div
          v-for="item in progressData.drawResults"
          :key="item.id"
          class="expert-item"
        >
         <expertItem :item="item.expert"></expertItem>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <div
      v-if="progressData && hasRole('FLOW_BM') && activity"
      class="bottom-actions"
    >
      <van-button class="back-btn" @click="goBack"> 返回 </van-button>
      <van-button
        type="primary"
        class="submit-btn"
        :loading="submitting"
        :disabled="submitting"
        @click="submitProgress"
      >
        {{ submitting ? "提交中..." : "提交审批" }}
      </van-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { showToast } from "vant";
import { expertApi } from "@/api/expert";
import moment from "moment";
import { useUserStore } from "@/stores";
import expertItem from "@/pages/pub/expert/component/list-item.vue";

const route = useRoute();
const router = useRouter();
const { userInfo, hasRole } = useUserStore();

// 响应式数据
const loading = ref(true);
const progressData = ref(null);
const approvalFormRef = ref(null);
const submitting = ref(false);
const activity = ref();

// 审批表单数据
const approvalForm = ref({
  approvalStatus: "",
  approvalContent: "",
});

// 当前流程步骤索引
const currentStepIndex = computed(() => {
  return getCurrentFlowIndex();
});

// 获取当前流程索引
const getCurrentFlowIndex = () => {
  if (!progressData.value?.flows) return 0;

  const flows = progressData.value.flows;

  // 找到第一个未完成的流程
  for (let i = 0; i < flows.length; i++) {
    const flow = flows[i];
    if (!flow.approvalStatus || flow.approvalStatus === "APPROVAL") {
      return i;
    }
  }

  // 如果所有流程都完成了，返回最后一个
  return flows.length - 1;
};

// 获取进度数据
const loadProgress = async () => {
  try {
    loading.value = true;
    const id = route.params.id;
    const response = await expertApi.evadeDetail(id);
    activity.value = response.data.flows.find((x) => x.activity) ? true : false;
    progressData.value = response.data;
  } catch (error) {
    console.error("加载进度失败:", error);
    showToast("加载进度失败");
  } finally {
    loading.value = false;
  }
};

// 格式化时间
const formatDateTime = (dateStr) => {
  if (!dateStr) return "-";
  return moment(dateStr).format("YYYY-MM-DD HH:mm:ss");
};

// 获取流程节点标题
const getFlowNodeTitle = (flow) => {
  return flow.conf?.flowNode || flow.conf?.name || "审批节点";
};

// 获取审批人标题
const getApproverTitle = (flow) => {
  if (flow.seq == 1) {
    return "经办人姓名";
  }
  return "审批人";
};

// 获取审批人姓名
const getApproverName = (flow) => {
  if (flow.activity) {
    return userInfo.nickname;
  } else {
    return flow.approvalUser?.nickname || "-";
  }
};

// 获取审批人联系方式
const getApproverContact = (flow) => {
  if (flow.activity) {
    return userInfo.phone;
  } else {
    return flow.approvalUser?.phone;
  }
};

// 获取审批人岗位
const getApproverPost = (flow) => {
  if (flow.activity) {
    return userInfo.post || "--";
  } else {
    return flow.approvalUser?.post || "--";
  }
};

// 获取审批人部门
const getApproverDept = (flow) => {
  if (flow.activity) {
    return userInfo.groups?.[0]?.name || "--";
  } else {
    return flow.approvalUser?.groups?.[0]?.name || "--";
  }
};

// 获取性别文本
const getGenderText = (gender) => {
  return window.$enums.getEnumText("Gender", gender);
};

// 返回
const goBack = () => {
  router.back();
};

// 提交审批（底部按钮）
const submitProgress = async () => {
  try {
    // 表单验证
    await approvalFormRef.value?.validate();

    submitting.value = true;
    const flow = progressData.value.flows.find((x) => x.activity);
    // 构造审批数据
    const approvalData = {
      id: flow.id,
      approvalStatus: approvalForm.value.approvalStatus,
      approvalContent: approvalForm.value.approvalContent,
    };

    // 调用审批接口
    await expertApi.submitApproval(flow.id, approvalData);

    showToast("审批提交成功");

    // 延迟跳转到结果页面
    setTimeout(() => {
      router.replace("/user/expert-apply/result");
    }, 1500);
  } finally {
    submitting.value = false;
  }
};

loadProgress();
</script>

<style lang="scss" scoped>
.progress-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 80px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #969799;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  padding: 20px;

  .van-button {
    margin-top: 16px;
  }
}

.progress-container {
  padding: 16px;
}

.steps-section {
  background: white;
  padding: 20px 16px;
  margin-bottom: 12px;
  border-radius: 8px;
}

.custom-steps {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  position: relative;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;

  &:not(:last-child) {
    margin-right: 16px;
  }
}

.step-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #ebedf0;
  color: #969799;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 8px;
  z-index: 2;
  position: relative;

  &.active {
    background: #07c160;
    color: white;
  }
}

.step-title {
  font-size: 12px;
  color: #646566;
  text-align: center;
  line-height: 1.2;
}

.step-line {
  position: absolute;
  top: 12px;
  left: calc(50% + 12px);
  right: calc(-50% + 12px);
  height: 1px;
  background: #ebedf0;
  z-index: 1;

  &.active {
    background: #07c160;
  }
}

.step-item:last-child .step-line {
  display: none;
}

.no-steps {
  text-align: center;
  padding: 20px;
  color: #969799;
  font-size: 14px;
}

.section {
  background: white;
  margin-bottom: 12px;
  border-radius: 8px;
  overflow: hidden;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  padding: 16px;
  border-bottom: 1px solid #ebedf0;
  background: #fafafa;
}

.section-header {
  padding: 16px;
  border-bottom: 1px solid #ebedf0;
  background: #fafafa;

  .project-code {
    display: block;
    font-size: 14px;
    color: #646566;
    margin-bottom: 4px;
  }

  .project-title {
    font-size: 16px;
    font-weight: 600;
    color: #323233;
    line-height: 1.4;
  }
}

.expert-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 12px 16px;
  border-top: 1px solid #ebedf0;
  display: flex;
  gap: 12px;
  z-index: 100;

  .back-btn {
    flex: 1;
    height: 44px;
    border: 1px solid #ebedf0;
    background: white;
    color: #646566;
  }

  .submit-btn {
    flex: 1;
    height: 44px;
    background: #07c160;
    border: none;
  }
}

// 覆盖vant样式
:deep(.van-cell) {
  padding: 12px 16px;

  .van-cell__title {
    font-size: 14px;
    color: #323233;
  }

  .van-cell__value {
    font-size: 14px;
    color: #646566;
  }
}
</style>
<route lang="json5">
{
  name: "EvadeProgress",
  meta: {
    title: "申请进度查询",
  },
}
</route>
