<!-- 专家规避审批页面 -->
<template>
  <div class="evade-detail">
    <van-cell-group>
      <van-cell title="项目名称" :value="progressData.name" />
      <van-cell title="规避理由" :value="progressData.evadeContent" />
    </van-cell-group>
    <div v-if="progressData.drawResults">
      <div v-for="item in progressData.drawResults" class="expert-item">
        <expertItem :item="item.expert" :showClass="true"></expertItem>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { showToast } from "vant";
import { expertApi } from "@/api/expert";
import moment from "moment";
import expertItem from "@/pages/pub/expert/component/list-item.vue";

// 响应式数据
const loading = ref(true);
const progressData = ref({});

const route = useRoute()

// 获取进度数据
const loadProgress = async () => {
  try {
    loading.value = true;
    const id = route.params.id;
    const response = await expertApi.evadeDetail(id);
    progressData.value = response.data;
  } finally {
    loading.value = false;
  }
};

// 页面初始化
loadProgress();
</script>

<style lang="scss" scoped>
.evade-detail {
  .expert-item {
  background: white;
  margin: 16px;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }
}
}
</style>
<route lang="json5">
{
  name: "EvadeDetail",
  meta: {
    title: "专家规避详情",
  },
}
</route>
