<!-- 专家资料库列表页面 -->
<template>
  <div class="expert-list-page">
    <FuniList
      ref="agencyListRef"
      :tabs="tabsConfig"
      @item-click="handleAgencyClick"
    >
      <template #item="{ item, index }">
        <div @click="itemClick(item)">
          <expertItem :item="item"></expertItem>
        </div>
      </template>
    </FuniList>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { useRouter } from "vue-router";
import { showToast } from "vant";
import useExpert from "@/hook/expert.js";
import expertItem from "./component/list-item.vue";

const router = useRouter();
const { getExpertList, expertTypes } = useExpert();

// Tabs 配置
const tabsConfig = [
  {
    key: "one",
    title: "专家",
    loadFunction: async (params) => {
      const response = await getExpertList(params);
      // 处理返回数据
      return {
        data: response.data,
        total: response.total,
      };
    },
    searchPlaceholder: "专家姓名",
    keyword: "name",
    filterConfig: [
      {
        type: "select",
        label: "类型",
        prop: "typeId",
        options: expertTypes,
      },
      {
        type: "select",
        label: "性别",
        prop: "gender",
        options: window.$enums.getEnums("Gender"),
      },
      {
        type: "input",
        label: "身份证号",
        prop: "idNum",
        props: {
          placeholder: "请输入身份证号",
          clearable: true,
        },
      },
      {
        type: "select",
        label: "服务范围",
        prop: "serviceRange",
        options: window.$enums.getEnums("ServiceRange"),
      },
      {
        type: "input",
        label: "手机号",
        prop: "phone",
        props: {
          placeholder: "请输入手机号",
          clearable: true,
        },
      },
      {
        type: "select",
        label: "是否启用",
        prop: "enabled",
        options: [
          {
            text: "是",
            value: true,
          },
          {
            text: "否",
            value: false,
          },
        ],
      },
    ],
  },
];

// 跳转到专家详情页
const itemClick = (item) => {
  router.push({ path: `/pub/expert/detail`, query: { id: item.id } });
};

// 页面初始化
onMounted(() => {});
</script>

<style lang="scss" scoped>
.expert-list-page {
  height: calc(100vh - 46px);
  display: flex;
  flex-direction: column;
  .expert-item {
    background: white;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    }
  }
}
</style>
<route lang="json5">
{
  name: "expert",
  meta: {
    title: "专家资料库",
  },
}
</route>
