<template>
  <!-- 专家头像和基本信息 -->
  <div class="expert-header">
    <div class="expert-avatar">
      <van-icon name="contact" />
    </div>
    <div class="expert-basic">
      <div class="expert-name">{{ item.name }}</div>
      <div class="expert-info">
        <span class="gender">{{ getGenderText(item.gender) }}</span>
        <span class="birthday">{{ item.birthday }}</span>
        <span class="id-num">{{ item.idNum }}</span>
      </div>
    </div>
  </div>

  <!-- 专家详细信息 -->
  <div class="expert-details">
    <div class="detail-row">
      <div class="detail-item">
        <span class="label">专家类型</span>
        <span class="value">{{ item.type?.name }}</span>
      </div>
      <div class="detail-item">
        <span class="label">单位职务</span>
        <span class="value">{{ item.post }}</span>
      </div>
    </div>

    <div class="detail-row">
      <div class="detail-item">
        <span class="label">所在领域</span>
        <span class="value">{{ item.field }}</span>
      </div>
      <div class="detail-item">
        <span class="label">专业级别(技术职称)</span>
        <span class="value">{{ item.level }}</span>
      </div>
    </div>

    <div class="detail-row">
      <div class="detail-item">
        <span class="label">手机号</span>
        <span class="value">{{ item.phone }}</span>
      </div>
      <div class="detail-item">
        <span class="label">座机号</span>
        <span class="value">{{ item.landline }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  item: [],
});

// 获取性别文本
const getGenderText = (gender) => {
  return window.$enums.getEnumText("Gender", gender);
};
</script>

<style lang="scss" scoped>
.expert-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }
}

.expert-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;

  .expert-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f7f8fa;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;

    .van-icon {
      font-size: 24px;
      color: #969799;
    }
  }

  .expert-basic {
    flex: 1;

    .expert-name {
      font-size: 16px;
      font-weight: 600;
      color: #323233;
      margin-bottom: 4px;
    }

    .expert-info {
      font-size: 12px;
      color: #646566;

      span {
        margin-right: 12px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}

.expert-details {
  .detail-row {
    display: flex;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .detail-item {
      flex: 1;
      display: flex;
      flex-direction: column;

      .label {
        font-size: 12px;
        color: #606266;
        margin-bottom: 4px;
      }

      .value {
        font-size: 14px;
        color: #000;
        font-weight: 500;
        &:empty::after {
          content: "--";
        }
      }
    }
  }
}
</style>
