/**
 * 专家抽取相关API接口
 */
export const expertApi = {
  /**
   * 提交专家抽取申请
   * @param {Object} data - 申请数据
   * @param {string} data.type - 项目类型 GOVERNMENT(机关) | DIRECTLY(直属单位)
   * @param {string} data.department - 需求部门（机关处室/直属单位）
   * @param {string} data.name - 专项资金（项目）名称
   * @param {number} data.funds - 专项资金金额
   * @param {string} data.reviewDate - 评审开始时间
   * @param {number} data.dateNum - 预计天数
   * @param {string} data.reviewPlace - 评审地点
   * @param {Array} data.attachments - 项目评审工作方案备案表
   * @param {string} data.attachmentsFirst - 项目初审意见反馈表
   * @param {Array} data.attachmentsOther - 项目初审意见反馈表-其他
   * @param {string} data.proposalExpertSources - 抽取专家类型 OUR(我局专家库) | PROVINCIAL(省财政厅专家库)
   * @param {Array} data.planDtos - 专家类型和人数配置
   * @param {number} data.departmentPlNum - 经办处室或直属单位人数
   * @param {number} data.inviteExpertPlNum - 邀请专家人数
   * @param {string} data.approvalLeader - 审批领导
   * @param {string} data.description - 补充说明
   * @param {string} data.applyUserName - 需求申请人姓名
   * @param {string} data.applyUserPhone - 需求申请人联系方式
   * @param {boolean} data.districtDep - 是否区县项目
   * @param {number} data.planExpertPlNum - 抽取专家人数
   * @param {Array} data.inviteDtos - 邀请专家列表
   * @returns {Promise} 返回申请结果
   */
  submitExpertApply: (data) => {
    if (data.id) {
      return window.$http.put("/api/v1/review-project-apply/" + data.id, data);
    } else {
      return window.$http.post("/api/v1/review-project-apply", data);
    }
  },
  //专家抽取详情
  getReviewProject: (data) => {
    return window.$http.fetch("/api/v1/review-project-apply/" + data.id);
  },

  /**
   * 获取专家类型列表
   * @param {Object} params - 查询参数
   * @param {number} [params.pageSize=1000] - 每页条数，默认1000获取所有数据
   * @param {boolean} [params.enabled=true] - 是否启用，默认true
   * @returns {Promise} 返回专家类型列表
   */
  getExpertTypes: (params = {}) => {
    const defaultParams = {
      pageSize: 1000,
      enabled: true,
      ...params,
    };
    return window.$http.fetch("/api/v1/expert-type", defaultParams);
  },

  /**
   * 获取专家库列表
   * @param {Object} params - 查询参数
   * @param {number} params.current - 当前页码
   * @param {number} params.size - 每页条数
   * @param {string} [params.expertName] - 专家姓名（可选）
   * @param {string} [params.field] - 专业领域（可选）
   * @param {string} [params.source] - 专家来源（可选）
   * @returns {Promise} 返回专家列表
   */
  getExpertList: (params) => {
    return window.$http.fetch("/api/v1/experts", params);
  },

  /**
   * 获取申请记录列表
   * @param {Object} params - 查询参数
   * @param {number} params.current - 当前页码
   * @param {number} params.size - 每页条数
   * @param {string} [params.status] - 申请状态（可选）
   * @param {string} [params.projectName] - 项目名称（可选）
   * @returns {Promise} 返回申请记录列表
   */
  getApplyRecords: (params) => {
    return window.$http.fetch("/api/v1/apply-records", params);
  },

  /**
   * 获取申请详情
   * @param {string} id - 申请ID
   * @returns {Promise} 返回申请详情
   */
  getApplyDetail: (id) => {
    return window.$http.fetch(`/api/v1/apply-records/${id}`);
  },

  /**
   * 暂存申请
   * @param {Object} data - 申请数据
   * @returns {Promise} 返回暂存结果
   */
  saveApplyDraft: (data) => {
    return window.$http.post("/api/v1/apply-draft", data);
  },

  /**
   * 获取暂存的申请
   * @returns {Promise} 返回暂存的申请数据
   */
  getApplyDraft: () => {
    return window.$http.fetch("/api/v1/apply-draft");
  },

  /**
   * 上传文件
   * @param {File} file - 文件对象
   * @param {string} type - 文件类型 attachments | attachmentsFirst | attachmentsOther
   * @returns {Promise} 返回上传结果
   */
  uploadFile: (file, type) => {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("type", type);

    return window.$http.post("/api/v1/upload", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
  },

  /**
   * 删除文件
   * @param {string} fileId - 文件ID
   * @returns {Promise} 返回删除结果
   */
  deleteFile: (fileId) => {
    return window.$http.delete(`/api/v1/files/${fileId}`);
  },

  /**
   * 获取部门列表
   * @param {string} type - 部门类型 GOVERNMENT | DIRECTLY
   * @returns {Promise} 返回部门列表
   */
  getDepartments: (type) => {
    return window.$http.fetch("/api/v1/departments", { type });
  },

  /**
   * 获取审批领导列表
   * @returns {Promise} 返回审批领导列表
   */
  getApprovalLeaders: () => {
    return window.$http.fetch("/api/v1/approval-leaders");
  },

  /**
   * 获取申请项目进度列表
   * @param {Object} params - 查询参数
   * @param {string} [params.districtDep] - 是否区县，区县不能发起流程
   * @param {string} [params.pageSize] - 每页条数，默认20
   * @param {string} [params.todoStatus] - 状态筛选 APPLY(已申请) | TODO(我的待办) | DONE(我的已办)
   * @param {string} [params.keyword] - 搜索关键词（项目名称/编号）
   * @returns {Promise} 返回申请项目进度列表
   */
  getApplyList: (params = {}) => {
    return window.$http.fetch("/api/v1/apply", params);
  },

  /**
   * 获取专家抽取申请详情
   * @param {string} id - 申请ID
   * @returns {Promise} 返回申请详情
   */
  getApplyDetail: (id) => {
    return window.$http.fetch(`/api/v1/review-project-apply/${id}`);
  },

  /**
   * 工作流程审批
   * @param {string} id - 流程ID
   * @param {Object} data - 审批数据
   * @param {string} data.approvalStatus - 审批结果 PASS(通过) | REJECT(驳回)
   * @param {string} data.approvalContent - 审批内容（必填）
   * @param {string} data.id - 流程ID
   * @returns {Promise} 返回审批结果
   */
  submitApproval: (id, data) => {
    return window.$http.put(`/api/v1/flow/${id}/apply`, data);
  },

  /**
   * 获取项目清单列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.pageSize - 每页数量
   * @param {string} params.projectStatus - 项目状态 NORMAL(正常) | CANCEL(作废)
   * @param {number} params.timestamp - 时间戳
   * @returns {Promise} 返回项目列表
   */
  getProjectList: (params) => {
    return window.$http.fetch("/api/v1/purchase-project", params);
  },
  /**
   * 作废
   * @param {*} params 
   * @returns 
   */
  projectCancel: (id) => {
    return window.$http.post("/api/v1/purchase-project/"+id+"/cancel");
  },
    /**
   * 详情
   * @param {*} params 
   * @returns 
   */
  projectDetail: (id) => {
    return window.$http.fetch("/api/v1/purchase-project/"+id);
  },

  /**
   * 创建项目清单
   * @param {Object} data - 项目数据
   * @param {string} data.name - 项目名称
   * @param {string} data.type - 项目分类 GOVERNMENT(机关) | DIRECTLY(直属单位)
   * @param {string} data.departmentId - 承办处室id
   * @param {string} data.purpose - 采购内容(用途)
   * @param {string} data.planType - 预算分类 GOV(政府采购项目) | UNGOV(非政府采购项目)
   * @param {string} data.items - 采购品目
   * @param {number} data.number - 数量
   * @param {number} data.fundsBudget - 经费预算
   * @param {string} data.projectDate - 立项时间
   * @param {boolean} data.districtDep - 是否区县项目
   * @returns {Promise} 返回创建结果
   */
  createProject: (data) => {
    return window.$http.post("/api/v1/purchase-project", data);
  },

  /**
   * 获取专家资料库列表
   * @param {Object} params - 查询参数
   * @param {number} params.page - 页码
   * @param {number} params.pageSize - 每页数量
   * @param {string} params.name - 专家姓名（搜索关键词）
   * @param {string} params.expertType - 专家类型
   * @param {string} params.gender - 性别 MALE(男) | FEMALE(女)
   * @returns {Promise} 返回专家列表
   */
  getExpertList: (params) => {
    return window.$http.fetch("/api/v1/expert", params);
  },

  /**
   * 获取专家详情
   * @param {string} id - 专家ID
   * @returns {Promise} 返回专家详情信息
   */
  getExpertDetail: (id) => {
    return window.$http.fetch(`/api/v1/expert/${id}`);
  },

  /**
   * 获取专家种类列表（管理端）
   * @param {Object} params - 查询参数
   * @param {number} [params.pageSize] - 每页条数，默认20
   * @param {string} [params.keyword] - 搜索关键词
   * @returns {Promise} 返回专家种类列表数据
   */
  getExpertCategories: (params = {}) => {
    return window.$http.fetch("/api/v1/expert/categories", {
      pageSize: 20,
      ...params,
    });
  },

  /**
   * 获取专家种类详情
   * @param {string} id - 专家种类ID
   * @returns {Promise} 返回专家种类详情
   */
  getExpertTypeDetail: (id) => {
    return window.$http.fetch(`/api/v1/expert-type/${id}`);
  },

  /**
   * 获取专家列表
   * @param {Object} params - 查询参数
   * @param {string} [params.pageSize] - 每页条数
   * @param {string} [params.name] - 专家姓名
   * @returns {Promise} 返回专家列表数据
   */
  getExpertList: (params = {}) => {
    return window.$http.fetch("/api/v1/expert", params);
  },

  /**
   * 创建专家
   * @param {Object} data - 专家数据
   * @returns {Promise} 返回创建结果
   */
  createExpert: (data) => {
    return window.$http.post("/api/v1/expert", data);
  },

  /**
   * 更新专家
   * @param {string} id - 专家ID
   * @param {Object} data - 专家数据
   * @returns {Promise} 返回更新结果
   */
  updateExpert: (id, data) => {
    return window.$http.put(`/api/v1/expert/${id}`, data);
  },

  /**
   * 删除专家
   * @param {string} id - 专家ID
   * @returns {Promise} 返回删除结果
   */
  deleteExpert: (id) => {
    return window.$http.delete(`/api/v1/expert/${id}`);
  },

  /**
   * 切换专家状态
   * @param {string} id - 专家ID
   * @param {boolean} enabled - 启用状态
   * @returns {Promise} 返回切换结果
   */
  toggleExpertStatus: (id, enabled) => {
    return window.$http.put(`/api/v1/expert/${id}/status`, { enabled });
  },

  /**
   * 创建专家（管理端）
   * @param {Object} data - 专家数据
   * @param {string} data.name - 专家姓名
   * @param {string} data.phone - 联系电话
   * @param {string} data.email - 邮箱
   * @param {string} data.field - 专业领域
   * @param {string} data.categoryId - 专家种类ID
   * @param {string} data.title - 职称
   * @param {string} data.workUnit - 工作单位
   * @param {string} data.education - 学历
   * @param {string} data.experience - 工作经验
   * @returns {Promise} 返回创建结果
   */
  createExpert: (data) => {
    return window.$http.post("/api/v1/expert", data);
  },

  /**
   * 更新专家（管理端）
   * @param {string} id - 专家ID
   * @param {Object} data - 更新数据
   * @returns {Promise} 返回更新结果
   */
  updateExpert: (id, data) => {
    return window.$http.put(`/api/v1/expert/${id}`, data);
  },

  /**
   * 删除专家（管理端）
   * @param {string} id - 专家ID
   * @returns {Promise} 返回删除结果
   */
  deleteExpert: (id) => {
    return window.$http.delete(`/api/v1/expert/${id}`);
  },

  /**
   * 启用/禁用专家（管理端）
   * @param {string} id - 专家ID
   * @param {boolean} enabled - 是否启用
   * @returns {Promise} 返回操作结果
   */
  toggleExpertStatus: (id, enabled) => {
    return window.$http.put(`/api/v1/expert/${id}/status`, { enabled });
  },

  /**
   * 创建专家种类（管理端）
   * @param {Object} data - 专家种类数据
   * @param {string} data.name - 种类名称
   * @param {string} data.description - 种类描述
   * @returns {Promise} 返回创建结果
   */
  createExpertCategory: (data) => {
    return window.$http.post("/api/v1/expert/categories", data);
  },

  /**
   * 更新专家种类（管理端）
   * @param {string} id - 种类ID
   * @param {Object} data - 更新数据
   * @returns {Promise} 返回更新结果
   */
  updateExpertCategory: (id, data) => {
    return window.$http.put(`/api/v1/expert/categories/${id}`, data);
  },

  /**
   * 删除专家种类（管理端）
   * @param {string} id - 种类ID
   * @returns {Promise} 返回删除结果
   */
  deleteExpertCategory: (id) => {
    return window.$http.delete(`/api/v1/expert/categories/${id}`);
  },

  /**
   * 申请专家规避
   * @param {string} id - 项目申请ID
   * @param {Object} data - 规避数据
   * @param {string} data.selectContent - 规避原因
   * @param {Array<string>} data.resultIds - 规避专家ID列表
   * @returns {Promise} 返回规避申请结果
   */
  systemEvade: (id, data) => {
    return window.$http.put(
      `/api/v1/review-project-apply/${id}/system-evade`,
      data
    );
  },
  /**
   * 专家规避详情页
   */
  evadeDetail: (id, data) => {
    return window.$http.fetch(`/api/v1/expert-evade-apply/${id}`, data);
  },
};
